TEST, seems still broken
tool output display is entirely broken, it needs to use the external printer
also, show (486 lines truncated) in red foreground text color

make Agent interrupted left aligned

use real prompt in
result.push("System prompt would be injected here based on CLI args and workspace".to_string());

ensure that there is newline at bottom of rendered ai message markdown, to better differentiate ai response to prompt input

fix 7. markdown demo display TwoThree in one line? they should be broken up

ensure flattened newlines before and after headings

using find and wc, refactor all files above 300 lines

make sender an enum proper IDE references
pub struct AppMessage {
pub sender: String,

is not always used correctly, there should only be one system
sender: "System".to_string(),

needs runtime model change capabilities
add gemini 2.0 flash provider ((has tools))
add cohere provider (has tools)

use just > for user messages to be even with custom prompt
and not the rounded border
ACTUALLY, use the rounded border only for AI messages
╭─ Response ───────────────────────────────────────────────────────────────────────────────────────────────────────╮
│

keep 1 space padding left and right inside