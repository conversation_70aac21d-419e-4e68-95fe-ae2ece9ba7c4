use rust_llm_tui::{app::{App, MessageContent, FormattedTextElement}, display};

fn main() {
    let test_markdown = r#"One

- First item
- Second item

Two

Three

1. First item
2. Second item

Four"#;

    println!("=== Raw markdown ===");
    println!("{}", test_markdown);
    println!("\n=== Parsed structure ===");
    
    let app = App::new(reqwest::Client::new());
    let app_message = app.create_ai_app_message_from_raw(test_markdown);
    
    for (i, part) in app_message.parts.iter().enumerate() {
        println!("Part {}: {:?}", i, part);
        if let MessageContent::FormattedText(elements) = part {
            for (j, element) in elements.iter().enumerate() {
                println!("  Element {}: {:?}", j, element);
            }
        }
    }
    
    println!("\n=== Rendered output ===");
    if let Err(e) = display::print_formatted_message(&app_message) {
        eprintln!("Error displaying message: {}", e);
    }
}
